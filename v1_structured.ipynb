# ================== IMPORTS ==================
import os
import re
import javalang
import pandas as pd
import json
import hashlib
from datetime import datetime
from collections import defaultdict, deque
from pathlib import Path
from neo4j import GraphDatabase

print("✅ All libraries imported successfully!")
print("📦 Key dependencies:")
print(f"   - javalang: {javalang.__version__ if hasattr(javalang, '__version__') else 'installed'}")
print(f"   - pandas: {pd.__version__}")
print(f"   - neo4j: Available")

# ================== HELPER FUNCTIONS ==================

def register_node(nodes, raw_node, file_path):
    """Register a node with enhanced metadata following reference structure."""
    if not raw_node or raw_node in nodes:
        return
    
    node_type, full_name = raw_node.split(":", 1)
    
    # Determine short name based on node type
    if node_type == "file":
        short_name = os.path.basename(full_name)
    elif node_type in ("folder", "project", "application", "package"):
        short_name = os.path.basename(full_name.rstrip("/\\"))
    else:
        short_name = full_name.split(".")[-1] if "." in full_name else full_name
    
    nodes[raw_node] = {
        "id": raw_node,
        "type": node_type,
        "name": short_name,
        "full_name": full_name,
        "file_path": file_path
    }

def add_relation(relations, existing_relations, src, rel, dst, file_path, nodes):
    """Add relation with duplicate detection and node registration."""
    if not src or not dst:
        return
    
    key = (src, rel, dst)
    if key in existing_relations:
        return
    
    existing_relations.add(key)
    register_node(nodes, src, file_path)
    register_node(nodes, dst, file_path)
    relations.append([src, rel, dst])

print("✅ Helper functions defined successfully!")
print("🔧 Available functions:")
print("   - register_node(): Registers nodes with enhanced metadata")
print("   - add_relation(): Adds relationships with duplicate detection")
print("   - Follows project/application/package hierarchy structure")

# ================== ENDPOINT AND USAGE TRACKING ==================

class EndpointUsageTracker:
    """Track API endpoints, their definitions, and usage patterns."""
    def __init__(self):
        self.endpoints = {}  # endpoint_path -> metadata
        self.endpoint_methods = {}  # method_id -> endpoint info
        self.method_calls = defaultdict(list)  # method -> [called_methods]
        self.data_operations = defaultdict(list)  # method -> [operations]
    
    def register_endpoint(self, path, method, http_method, class_name, method_name, file_path):
        """Register an API endpoint with its definition location."""
        endpoint_key = f"{http_method}:{path}"
        method_id = f"method:{class_name}.{method_name}"
        
        if endpoint_key not in self.endpoints:
            self.endpoints[endpoint_key] = {
                'path': path,
                'http_method': http_method,
                'defined_in_class': class_name,
                'defined_in_method': method_name,
                'file_path': file_path,
                'method_id': method_id
            }
        
        self.endpoint_methods[method_id] = endpoint_key
        return endpoint_key
    
    def add_method_call(self, caller_method, called_method, operation_type='call'):
        """Track method calls and their operation types."""
        self.method_calls[caller_method].append({
            'called_method': called_method,
            'operation_type': operation_type
        })
    
    def add_data_operation(self, method_id, operation_type, target, details=None):
        """Track data operations performed by methods."""
        self.data_operations[method_id].append({
            'operation_type': operation_type,
            'target': target,
            'details': details or {}
        })
    
    def get_endpoint_usage(self, endpoint_key):
        """Get detailed usage information for an endpoint."""
        if endpoint_key not in self.endpoints:
            return None
        
        endpoint_info = self.endpoints[endpoint_key]
        method_id = endpoint_info['method_id']
        
        return {
            'endpoint': endpoint_info,
            'method_calls': self.method_calls.get(method_id, []),
            'data_operations': self.data_operations.get(method_id, [])
        }

print("✅ Endpoint Usage Tracker created successfully!")
print("📊 Tracker capabilities:")
print("   - Register API endpoints with their definitions")
print("   - Track method calls and data operations")
print("   - Analyze endpoint usage patterns")

class ApplicationNodeExtractor:
    """Extract application-specific nodes and relationships."""
    
    @staticmethod
    def extract_spring_annotations(code):
        """Extract Spring framework annotations and their configurations."""
        annotations = []
        patterns = {
            'controller': r'@(?:RestController|Controller)(?:\([^)]*\))?',
            'service': r'@Service(?:\([^)]*\))?',
            'repository': r'@Repository(?:\([^)]*\))?',
            'component': r'@Component(?:\([^)]*\))?',
            'configuration': r'@Configuration(?:\([^)]*\))?',
            'autowired': r'@Autowired',
            'value': r'@Value\("([^"]*)"\)',
            'value_single': r"@Value\('([^']*)'\)",
            'qualifier': r'@Qualifier\("([^"]*)"\)',
            'qualifier_single': r"@Qualifier\('([^']*)'\)",
            'transactional': r'@Transactional(?:\([^)]*\))?',
            'cacheable': r'@Cacheable(?:\([^)]*\))?'
        }
        
        for annotation_type, pattern in patterns.items():
            try:
                for match in re.finditer(pattern, code):
                    # Clean up annotation type name (remove _single suffix)
                    clean_type = annotation_type.replace('_single', '')
                    annotations.append({
                        'type': clean_type,
                        'full_match': match.group(0),
                        'value': match.group(1) if match.groups() else None,
                        'start': match.start(),
                        'end': match.end()
                    })
            except re.error as e:
                print(f"⚠️ Regex error in pattern '{annotation_type}': {e}")
                continue
        
        return annotations
    
    @staticmethod
    def extract_jpa_entities(code):
        """Extract JPA entity information."""
        entities = []
        
        # Entity annotations
        patterns = {
            'entity': r'@Entity(?:\([^)]*\))?',
            'table': r'@Table\(name\s*=\s*"([^"]*)"',
            'table_single': r"@Table\(name\s*=\s*'([^']*)'" ,
            'column': r'@Column\(name\s*=\s*"([^"]*)"',
            'column_single': r"@Column\(name\s*=\s*'([^']*)'" ,
            'id': r'@Id'
        }
        
        result = {'entities': [], 'tables': [], 'columns': []}
        
        for pattern_type, pattern in patterns.items():
            try:
                for match in re.finditer(pattern, code):
                    if pattern_type == 'entity':
                        result['entities'].append({
                            'type': 'entity',
                            'annotation': match.group(0)
                        })
                    elif pattern_type.startswith('table'):
                        result['tables'].append(match.group(1))
                    elif pattern_type.startswith('column'):
                        result['columns'].append(match.group(1))
            except re.error as e:
                print(f"⚠️ Regex error in JPA pattern '{pattern_type}': {e}")
                continue
        
        return result

print("✅ Application Node Extractor created successfully!")
print("📊 Extractor capabilities:")
print("   - Spring annotations (Controller, Service, Repository, etc.)")
print("   - JPA entities and mappings")
print("   - Configuration properties")

# SQL stopwords for filtering
SQL_STOPWORDS = {
    "select","from","where","group","order","by","join","on","as","and","or",
    "if","then","else","when","end","case","distinct","limit","offset",
    "like","not","null","is","inner","left","right","outer","full","cross"
}

# Enhanced node types for comprehensive Java application coverage
JAVA_NODE_TYPES = {
    'structural': ['package', 'folder', 'file', 'class', 'interface', 'enum', 'annotation'],
    'behavioral': ['method', 'constructor', 'lambda', 'operation', 'condition'],
    'data': ['variable', 'field', 'parameter', 'return_value', 'constant'],
    'persistence': ['table', 'column', 'index', 'constraint', 'view', 'procedure'],
    'integration': ['api_endpoint', 'message_queue', 'cache', 'external_service'],
    'configuration': ['property', 'profile', 'bean', 'component_scan'],
    'security': ['role', 'permission', 'authentication', 'authorization'],
    'monitoring': ['metric', 'log', 'trace', 'health_check']
}

RELATIONSHIP_TYPES = {
    'structural': ['CONTAINS', 'DECLARES', 'EXTENDS', 'IMPLEMENTS', 'IMPORTS'],
    'behavioral': ['CALLS', 'INVOKES', 'RETURNS', 'THROWS', 'HANDLES'],
    'data_flow': ['READS', 'WRITES', 'TRANSFORMS', 'PRODUCES', 'CONSUMES'],
    'dependency': ['DEPENDS_ON', 'INJECTS', 'AUTOWIRES', 'CONFIGURES'],
    'persistence': ['MAPS_TO', 'JOINS', 'REFERENCES', 'CASCADES'],
    'integration': ['CALLS_API', 'PUBLISHES', 'SUBSCRIBES', 'CACHES'],
    'security': ['SECURES', 'AUTHORIZES', 'AUTHENTICATES', 'VALIDATES']
}

print("✅ Constants and configuration loaded successfully!")
print(f"📊 Configuration summary:")
print(f"   - SQL stopwords: {len(SQL_STOPWORDS)} terms")
print(f"   - Java node types: {sum(len(v) for v in JAVA_NODE_TYPES.values())} types across {len(JAVA_NODE_TYPES)} categories")
print(f"   - Relationship types: {sum(len(v) for v in RELATIONSHIP_TYPES.values())} types across {len(RELATIONSHIP_TYPES)} categories")
print(f"\n🏗️ Node type categories:")
for category, types in JAVA_NODE_TYPES.items():
    print(f"   - {category}: {len(types)} types")

def extract_db_table_usage(code):
    """Enhanced database table usage extraction with detailed operations."""
    operations = {
        'reads': set(),
        'writes': set(),
        'deletes': set(),
        'creates': set(),
        'alters': set()
    }
    
    # Enhanced patterns for different SQL operations
    patterns = [
        # Read operations
        (r'\bFROM\s+([A-Za-z_][\w]*)', operations['reads']),
        (r'\bJOIN\s+([A-Za-z_][\w]*)', operations['reads']),
        (r'@Query\s*\([^)]*["\'][^"\']*(?:SELECT|select).*?(?:FROM|from)\s+([A-Za-z_][\w]*)', operations['reads']),
        
        # Write operations
        (r'\bUPDATE\s+([A-Za-z_][\w]*)', operations['writes']),
        (r'\bINSERT\s+INTO\s+([A-Za-z_][\w]*)', operations['writes']),
        (r'@Modifying.*?UPDATE\s+([A-Za-z_][\w]*)', operations['writes']),
        
        # Delete operations
        (r'\bDELETE\s+FROM\s+([A-Za-z_][\w]*)', operations['deletes']),
        (r'@Modifying.*?DELETE\s+FROM\s+([A-Za-z_][\w]*)', operations['deletes']),
        
        # DDL operations
        (r'\bCREATE\s+TABLE\s+([A-Za-z_][\w]*)', operations['creates']),
        (r'\bALTER\s+TABLE\s+([A-Za-z_][\w]*)', operations['alters'])
    ]
    
    for pattern, target_set in patterns:
        try:
            for match in re.findall(pattern, code, re.IGNORECASE | re.DOTALL):
                table = match.strip().lower()
                if len(table) >= 2 and table not in SQL_STOPWORDS and not table.isdigit():
                    target_set.add(table)
        except re.error as e:
            print(f"⚠️ Regex error in DB extraction: {e}")
            continue
    
    # Extract JPA repository methods
    jpa_patterns = [
        (r'findBy([A-Za-z]+)', operations['reads']),
        (r'save\(([A-Za-z]+)', operations['writes']),
        (r'delete\(([A-Za-z]+)', operations['deletes'])
    ]
    
    for pattern, target_set in jpa_patterns:
        try:
            for match in re.findall(pattern, code):
                entity = match.strip().lower()
                if len(entity) >= 2:
                    target_set.add(f"entity_{entity}")
        except re.error as e:
            print(f"⚠️ Regex error in JPA extraction: {e}")
            continue
    
    return operations

def extract_api_endpoints(code):
    """Enhanced API endpoint extraction with HTTP methods and parameters."""
    endpoints = []
    
    # Spring REST mappings with HTTP methods - Fixed regex patterns
    mapping_patterns = {
        'GET': r'@GetMapping\("([^"]*)"\)',
        'GET_SINGLE': r"@GetMapping\('([^']*)'\)",
        'POST': r'@PostMapping\("([^"]*)"\)',
        'POST_SINGLE': r"@PostMapping\('([^']*)'\)",
        'PUT': r'@PutMapping\("([^"]*)"\)',
        'PUT_SINGLE': r"@PutMapping\('([^']*)'\)",
        'DELETE': r'@DeleteMapping\("([^"]*)"\)',
        'DELETE_SINGLE': r"@DeleteMapping\('([^']*)'\)",
        'PATCH': r'@PatchMapping\("([^"]*)"\)',
        'PATCH_SINGLE': r"@PatchMapping\('([^']*)'\)",
        'REQUEST': r'@RequestMapping\([^)]*value\s*=\s*"([^"]*)"',
        'REQUEST_SINGLE': r"@RequestMapping\([^)]*value\s*=\s*'([^']*)'"  
    }
    
    for method, pattern in mapping_patterns.items():
        try:
            for match in re.finditer(pattern, code):
                path = match.group(1)
                if path:
                    # Clean up method name (remove _SINGLE suffix)
                    clean_method = method.replace('_SINGLE', '')
                    endpoints.append({
                        'path': path,
                        'method': clean_method,
                        'type': 'rest_endpoint'
                    })
        except re.error as e:
            print(f"⚠️ Regex error in API pattern '{method}': {e}")
            continue
    
    # Extract path variables and request parameters
    path_variables = []
    request_params = []
    
    try:
        path_var_pattern = r'@PathVariable\(["\']?([^"\')]*)["\']?\)'
        path_variables = re.findall(path_var_pattern, code)
        
        request_param_pattern = r'@RequestParam\(["\']?([^"\')]*)["\']?\)'
        request_params = re.findall(request_param_pattern, code)
    except re.error as e:
        print(f"⚠️ Regex error in parameter extraction: {e}")
    
    return {
        'endpoints': endpoints,
        'path_variables': path_variables,
        'request_params': request_params
    }

print("✅ Extraction functions created successfully!")
print("📊 Available extraction capabilities:")
print("   - Database operations (SELECT, INSERT, UPDATE, DELETE)")
print("   - API endpoints (REST mappings and paths)")
print("   - Path variables and request parameters")

# Expression handler removed - not needed for endpoint-focused analysis
print("✅ Simplified extraction approach - focusing on project structure and endpoints")

def extract_relations_enhanced(project_path):
    """Enhanced extraction following project/application/package structure."""
    print(f"🚀 Starting enhanced extraction for: {project_path}")
    
    nodes = {}
    relations = []
    existing_relations = set()
    endpoint_tracker = EndpointUsageTracker()
    
    # Project hierarchy setup
    project_name = os.path.basename(os.path.abspath(project_path))
    project_node = f"project:{project_name}"
    
    java_files = []
    
    # Pass 1: Build project/application/package hierarchy
    print("📁 Phase 1: Building project hierarchy...")
    for root, dirs, files in os.walk(project_path):
        rel_root = os.path.relpath(root, project_path)
        abs_root = os.path.join(project_path, rel_root)
        
        path_parts = rel_root.split(os.sep)
        
        # Determine node type based on hierarchy level
        if rel_root == ".":
            current_node = project_node
        elif len(path_parts) == 1:
            current_node = f"application:{abs_root}"
            add_relation(relations, existing_relations, project_node, "contains", current_node, abs_root, nodes)
        else:
            current_node = f"package:{abs_root}"
            parent_path = os.path.join(project_path, *path_parts[:-1])
            parent_node = f"application:{parent_path}" if len(path_parts) == 2 else f"package:{parent_path}"
            add_relation(relations, existing_relations, parent_node, "contains", current_node, abs_root, nodes)
        
        # Process subdirectories
        for d in dirs:
            subfolder_rel = os.path.relpath(os.path.join(root, d), project_path)
            subfolder_abs = os.path.join(project_path, subfolder_rel)
            sub_path_parts = subfolder_rel.split(os.sep)
            
            if len(sub_path_parts) == 1:
                sub_node = f"application:{subfolder_abs}"
            else:
                sub_node = f"package:{subfolder_abs}"
            
            add_relation(relations, existing_relations, current_node, "contains", sub_node, abs_root, nodes)
        
        # Process Java files
        for file in files:
            if file.endswith(".java"):
                java_files.append(os.path.join(root, file))
    
    print(f"   Found {len(java_files)} Java files")

    # Pass 2: Parse Java files
    print("☕ Phase 2: Parsing Java files...")
    parsed_files = {}
    
    for file_path in java_files:
        with open(file_path, "r", encoding="utf-8") as f:
            try:
                parsed_files[file_path] = javalang.parse.parse(f.read())
            except javalang.parser.JavaSyntaxError:
                continue
    
    print(f"   Successfully parsed {len(parsed_files)} files")

    # Pass 3: File to folder mapping
    print("📁 Phase 3: Mapping files to hierarchy...")
    for file_path, tree in parsed_files.items():
        rel_path = os.path.relpath(file_path, project_path)
        abs_path = os.path.join(project_path, rel_path)
        
        folder_path = os.path.dirname(abs_path)
        folder_parts = os.path.relpath(folder_path, project_path).split(os.sep)
        
        if len(folder_parts) == 1:
            folder_node = f"application:{folder_path}"
        elif len(folder_parts) >= 2:
            folder_node = f"package:{folder_path}"
        else:
            folder_node = project_node
        
        file_node = f"file:{abs_path}"
        add_relation(relations, existing_relations, folder_node, "contains", file_node, abs_path, nodes)

    # Pass 4: Extract class relationships and endpoints
    print("🔍 Phase 4: Extracting relationships and endpoints...")
    processed_files = 0
    
    for file_path, tree in parsed_files.items():
        try:
            rel_path = os.path.relpath(file_path, project_path)
            abs_path = os.path.join(project_path, rel_path)
            file_node = f"file:{abs_path}"
            
            # Get imports and package info
            import_map = {}
            package_name = tree.package.name if tree.package else None
            
            for imp in tree.imports:
                if imp.path and not imp.wildcard and not imp.path.startswith(("java.", "javax.")):
                    class_name = imp.path.split('.')[-1]
                    import_map[class_name] = imp.path

            # Process class and interface declarations

            for type_decl in tree.types:
                if not isinstance(type_decl, (javalang.tree.ClassDeclaration, javalang.tree.InterfaceDeclaration)):
                    continue

                decl_type = "class" if isinstance(type_decl, javalang.tree.ClassDeclaration) else "interface"
                full_decl_name = f"{package_name}.{type_decl.name}" if package_name else type_decl.name
                decl_node = f"{decl_type}:{full_decl_name}"
                add_relation(relations, existing_relations, file_node, "declares", decl_node, rel_path, nodes)

                # Class/Interface variables (fields)
                for field in getattr(type_decl, "fields", []):
                    for decl in field.declarators:
                        var_name = decl.name
                        var_node = f"variable:{full_decl_name}.{var_name}"
                        add_relation(relations, existing_relations, decl_node, "has_variable", var_node, rel_path, nodes)

                # Class implements interface
                if isinstance(type_decl, javalang.tree.ClassDeclaration) and type_decl.implements:
                    for impl in type_decl.implements:
                        interface_name = impl.name
                        if interface_name in import_map:
                            impl_full = import_map[interface_name]
                            add_relation(relations, existing_relations, decl_node, "implements", f"interface:{impl_full}", rel_path, nodes)
                
                # Class/Interface extends
                if type_decl.extends:
                    if isinstance(type_decl.extends, list):
                        for ext in type_decl.extends:
                            if ext.name in import_map:
                                ext_full = import_map[ext.name]
                                add_relation(relations, existing_relations, decl_node, "extends", f"{decl_type}:{ext_full}", rel_path, nodes)
                    else:
                        ext = type_decl.extends
                        if ext.name in import_map:
                            ext_full = import_map[ext.name]
                            add_relation(relations, existing_relations, decl_node, "extends", f"{decl_type}:{ext_full}", rel_path, nodes)

                # Methods
                for method in getattr(type_decl, "methods", []):
                    method_node = f"method:{full_decl_name}.{method.name}"
                    add_relation(relations, existing_relations, decl_node, "has_method", method_node, rel_path, nodes)
                    
                    # Check for API endpoints in method annotations
                    if hasattr(method, 'annotations') and method.annotations:
                        for annotation in method.annotations:
                            annotation_name = annotation.name
                            # Debug: Print found annotations for first few files
                            if processed_files <= 3:
                                print(f"      Found annotation: {annotation_name} on method {method.name}")
                            
                            if annotation_name in ['GetMapping', 'PostMapping', 'PutMapping', 'DeleteMapping', 'PatchMapping', 'RequestMapping']:
                                # Extract endpoint path from annotation
                                endpoint_path = '/unknown'
                                if hasattr(annotation, 'element') and annotation.element:
                                    if isinstance(annotation.element, list) and annotation.element:
                                        endpoint_path = annotation.element[0].value if hasattr(annotation.element[0], 'value') else '/unknown'
                                    elif hasattr(annotation.element, 'value'):
                                        endpoint_path = annotation.element.value
                                
                                # Register endpoint with tracker
                                http_method = annotation.name.replace('Mapping', '').upper()
                                if http_method == 'REQUEST':
                                    http_method = 'GET'  # Default for RequestMapping
                                
                                endpoint_key = endpoint_tracker.register_endpoint(
                                    endpoint_path, method.name, http_method, full_decl_name, method.name, file_path
                                )
                                
                                # Create endpoint node
                                endpoint_node = f"endpoint:{endpoint_key}"
                                add_relation(relations, existing_relations, method_node, "exposes", endpoint_node, rel_path, nodes)
                                
                                print(f"      ✅ Registered endpoint: {http_method} {endpoint_path} -> {full_decl_name}.{method.name}")

                    if not method.body:
                        continue
                    
                    # Track method variables and calls
                    declared_var_types = {}
                    
                    for path, node in method:
                        # Local variable declarations
                        if isinstance(node, javalang.tree.LocalVariableDeclaration):
                            for decl in node.declarators:
                                var_name = decl.name
                                var_node = f"variable:{full_decl_name}.{method.name}.{var_name}"
                                add_relation(relations, existing_relations, method_node, "uses", var_node, rel_path, nodes)
                                
                                # Track variable type
                                if hasattr(node.type, 'name'):
                                    type_name = node.type.name
                                    declared_var_types[var_name] = type_name
                                    if type_name in import_map:
                                        imp_class = import_map[type_name]
                                        add_relation(relations, existing_relations, var_node, "instance_of", f"class:{imp_class}", rel_path, nodes)
                        
                        # Method calls
                        elif isinstance(node, javalang.tree.MethodInvocation):
                            called_method_node = None
                            qualifier_name = None
                            
                            # Extract qualifier name properly
                            if node.qualifier:
                                if hasattr(node.qualifier, 'member'):
                                    qualifier_name = node.qualifier.member
                                elif hasattr(node.qualifier, 'name'):
                                    qualifier_name = node.qualifier.name
                                else:
                                    qualifier_name = str(node.qualifier)
                            
                            if qualifier_name and qualifier_name in declared_var_types:
                                type_name = declared_var_types[qualifier_name]
                                if type_name in import_map:
                                    imp_class = import_map[type_name]
                                    called_method_node = f"method:{imp_class}.{node.member}"
                            elif qualifier_name:
                                # Check if it's a class name (starts with uppercase)
                                if qualifier_name[0].isupper():
                                    called_method_node = f"method:{qualifier_name}.{node.member}"
                                else:
                                    # Assume it's a variable of the current class
                                    called_method_node = f"method:{full_decl_name}.{node.member}"
                            else:
                                # No qualifier - method call on current class
                                called_method_node = f"method:{full_decl_name}.{node.member}"
                            
                            if called_method_node:
                                add_relation(relations, existing_relations, method_node, "calls", called_method_node, rel_path, nodes)
                                # Track method call in endpoint tracker
                                endpoint_tracker.add_method_call(method_node, called_method_node)

            processed_files += 1
            if processed_files % 10 == 0:
                print(f"   Processed {processed_files}/{len(parsed_files)} files...")
                
        except Exception as e:
            print(f"   ⚠️ Error processing {os.path.basename(file_path)}: {e}")
            continue

    # Generate summary
    endpoint_summary = {
        'total_endpoints': len(endpoint_tracker.endpoints),
        'unique_paths': len(set(ep['path'] for ep in endpoint_tracker.endpoints.values())),
        'method_mappings': len(endpoint_tracker.endpoint_methods)
    }
    
    print(f"\n✅ Extraction completed successfully!")
    print(f"📊 Final statistics:")
    print(f"   - Total nodes: {len(nodes)}")
    print(f"   - Total relations: {len(relations)}")
    print(f"   - Endpoints found: {endpoint_summary['total_endpoints']}")
    print(f"   - Unique paths: {endpoint_summary['unique_paths']}")
    
    return {
        'nodes': list(nodes.values()),
        'relations': relations,
        'endpoint_tracker': endpoint_tracker,
        'endpoint_summary': endpoint_summary
    }

print("✅ Main extraction function created successfully!")
print("🚀 Ready to process Java projects with comprehensive analysis")

def save_enhanced_graph_to_csv(graph_data, output_dir="enhanced_graph_csv"):
    """Save enhanced graph data with lineage information to CSV files."""
    print(f"💾 Saving graph data to CSV files in: {output_dir}")
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Save nodes
    nodes_df = pd.DataFrame(graph_data['nodes'])
    nodes_df.to_csv(os.path.join(output_dir, "nodes.csv"), index=False)
    print(f"   ✅ Saved {len(nodes_df)} nodes to nodes.csv")
    
    # Save relations
    rel_df = pd.DataFrame(graph_data['relations'], columns=["src", "rel", "dst"])
    rel_df.to_csv(os.path.join(output_dir, "relations.csv"), index=False)
    print(f"   ✅ Saved {len(rel_df)} relations to relations.csv")
    
    # Save endpoints
    if graph_data['endpoint_tracker'].endpoints:
        endpoints_df = pd.DataFrame(graph_data['endpoint_tracker'].endpoints.values())
        endpoints_df.to_csv(os.path.join(output_dir, "endpoints.csv"), index=False)
        print(f"   ✅ Saved {len(endpoints_df)} endpoints to endpoints.csv")
    
    # Save method calls
    if graph_data['endpoint_tracker'].method_calls:
        method_calls_data = []
        for method, calls in graph_data['endpoint_tracker'].method_calls.items():
            for call in calls:
                method_calls_data.append({
                    'caller_method': method,
                    'called_method': call['called_method'],
                    'operation_type': call['operation_type']
                })
        if method_calls_data:
            calls_df = pd.DataFrame(method_calls_data)
            calls_df.to_csv(os.path.join(output_dir, "method_calls.csv"), index=False)
            print(f"   ✅ Saved {len(calls_df)} method calls to method_calls.csv")
    
    # Save data operations
    if graph_data['endpoint_tracker'].data_operations:
        data_ops_data = []
        for method, operations in graph_data['endpoint_tracker'].data_operations.items():
            for op in operations:
                data_ops_data.append({
                    'method': method,
                    'operation_type': op['operation_type'],
                    'target': op['target'],
                    'details': str(op['details'])
                })
        if data_ops_data:
            ops_df = pd.DataFrame(data_ops_data)
            ops_df.to_csv(os.path.join(output_dir, "data_operations.csv"), index=False)
            print(f"   ✅ Saved {len(ops_df)} data operations to data_operations.csv")
    
    # Save endpoint summary
    summary_df = pd.DataFrame([graph_data['endpoint_summary']])
    summary_df.to_csv(os.path.join(output_dir, "endpoint_summary.csv"), index=False)
    print(f"   ✅ Saved endpoint summary to endpoint_summary.csv")
    
    print(f"\n📁 All CSV files saved successfully in: {output_dir}")
    return output_dir

print("✅ CSV export function created successfully!")
print("💾 Will save comprehensive data including:")
print("   - nodes.csv: All graph nodes with metadata")
print("   - relations.csv: All relationships between nodes")
print("   - endpoints.csv: API endpoints with definitions")
print("   - method_calls.csv: Method call relationships")
print("   - data_operations.csv: Data operations performed")
print("   - endpoint_summary.csv: Summary statistics")

def push_enhanced_graph_to_neo4j(graph_data, uri, user, password, database="neo4j"):
    """Push enhanced graph data with lineage information to Neo4j."""
    print(f"🔗 Connecting to Neo4j at {uri}...")
    
    try:
        driver = GraphDatabase.driver(uri, auth=(user, password))
        print(f"✅ Connected to Neo4j successfully")
    except Exception as e:
        print(f"❌ Failed to connect to Neo4j: {e}")
        return
    
    try:
        with driver.session(database=database) as session:
            # Clear existing data
            print("🧹 Clearing existing data...")
            session.run("MATCH (n) DETACH DELETE n")
            
            # Create constraints for better performance
            print("🔧 Creating constraints...")
            constraints = [
                "CREATE CONSTRAINT IF NOT EXISTS FOR (n:File) REQUIRE n.id IS UNIQUE",
                "CREATE CONSTRAINT IF NOT EXISTS FOR (n:Class) REQUIRE n.id IS UNIQUE",
                "CREATE CONSTRAINT IF NOT EXISTS FOR (n:Method) REQUIRE n.id IS UNIQUE",
                "CREATE CONSTRAINT IF NOT EXISTS FOR (n:Variable) REQUIRE n.id IS UNIQUE",
                "CREATE CONSTRAINT IF NOT EXISTS FOR (n:Operation) REQUIRE n.id IS UNIQUE"
            ]
            
            for constraint in constraints:
                try:
                    session.run(constraint)
                except Exception as e:
                    print(f"   ⚠️ Constraint warning: {e}")

            # Push nodes with enhanced properties
            print(f"📊 Pushing {len(graph_data['nodes'])} nodes...")
            node_count = 0
            for n in graph_data['nodes']:
                # Clean None values and ensure all properties are serializable
                clean_props = {k: v for k, v in n.items() if v is not None}
                
                # Convert complex objects to strings
                for key, value in clean_props.items():
                    if isinstance(value, (dict, list)):
                        clean_props[key] = json.dumps(value)
                
                node_type = clean_props.get('type', 'Unknown').capitalize()
                session.run(
                    f"MERGE (a:{node_type} {{id:$id}}) SET a += $props",
                    id=clean_props["id"], props=clean_props
                )
                node_count += 1
                
                if node_count % 1000 == 0:
                    print(f"   Processed {node_count} nodes...")

            # Push relationships with batching
            print(f"🔗 Pushing {len(graph_data['relations'])} relationships...")
            rel_count = 0
            batch_size = 1000
            relations_list = list(graph_data['relations'])
            
            for i in range(0, len(relations_list), batch_size):
                batch = relations_list[i:i + batch_size]
                batch_data = [{'src': src, 'rel': rel, 'dst': dst} for src, rel, dst in batch]
                
                # Simple relationship creation query
                query = """
                    UNWIND $batch as row
                    MATCH (a {id: row.src}), (b {id: row.dst})
                    WITH a, b, row.rel as rel_type
                    FOREACH (x IN CASE WHEN rel_type = 'contains' THEN [1] ELSE [] END |
                        MERGE (a)-[:CONTAINS]->(b)
                    )
                    FOREACH (x IN CASE WHEN rel_type = 'declares' THEN [1] ELSE [] END |
                        MERGE (a)-[:DECLARES]->(b)
                    )
                    FOREACH (x IN CASE WHEN rel_type = 'has_method' THEN [1] ELSE [] END |
                        MERGE (a)-[:HAS_METHOD]->(b)
                    )
                    FOREACH (x IN CASE WHEN rel_type = 'has_variable' THEN [1] ELSE [] END |
                        MERGE (a)-[:HAS_VARIABLE]->(b)
                    )
                    FOREACH (x IN CASE WHEN rel_type = 'calls' THEN [1] ELSE [] END |
                        MERGE (a)-[:CALLS]->(b)
                    )
                    FOREACH (x IN CASE WHEN rel_type = 'uses' THEN [1] ELSE [] END |
                        MERGE (a)-[:USES]->(b)
                    )
                    FOREACH (x IN CASE WHEN rel_type = 'implements' THEN [1] ELSE [] END |
                        MERGE (a)-[:IMPLEMENTS]->(b)
                    )
                    FOREACH (x IN CASE WHEN rel_type = 'extends' THEN [1] ELSE [] END |
                        MERGE (a)-[:EXTENDS]->(b)
                    )
                    FOREACH (x IN CASE WHEN rel_type = 'exposes' THEN [1] ELSE [] END |
                        MERGE (a)-[:EXPOSES]->(b)
                    )
                    FOREACH (x IN CASE WHEN rel_type = 'instance_of' THEN [1] ELSE [] END |
                        MERGE (a)-[:INSTANCE_OF]->(b)
                    )
                    RETURN count(*) as processed
                """
                
                try:
                    result = session.run(query, batch=batch_data)
                    rel_count += len(batch)
                    if (i // batch_size + 1) % 5 == 0:
                        print(f"   Processed {rel_count} relationships...")
                except Exception as e:
                    print(f"   ⚠️ Error in batch {i//batch_size + 1}: {e}")
            
            print(f"\n✅ Successfully pushed to Neo4j:")
            print(f"   - {node_count} nodes")
            print(f"   - {rel_count} relationships")
            print(f"   - {graph_data['endpoint_summary']['total_endpoints']} endpoints tracked")
            print(f"   - {graph_data['endpoint_summary']['unique_paths']} unique paths")
    
    except Exception as e:
        print(f"❌ Error during Neo4j operations: {e}")
    finally:
        driver.close()
        print("🔌 Neo4j connection closed")

print("✅ Neo4j export function created successfully!")
print("🔗 Features:")
print("   - Automatic constraint creation for performance")
print("   - Batch processing for large datasets")
print("   - Comprehensive error handling")
print("   - Progress tracking during upload")

# Project configuration
project_path = r"OneInsights"

# Neo4j configuration
NEO4J_URI = "bolt://localhost:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "Test@7889"
NEO4J_DB = "my-oneinsights"

# Output configuration
CSV_OUTPUT_DIR = "enhanced_graph_output"

print("⚙️ Configuration loaded:")
print(f"   - Project path: {project_path}")
print(f"   - Neo4j URI: {NEO4J_URI}")
print(f"   - Neo4j Database: {NEO4J_DB}")
print(f"   - CSV output directory: {CSV_OUTPUT_DIR}")

# Check if project path exists
if os.path.exists(project_path):
    print(f"✅ Project directory found: {project_path}")
    java_file_count = sum(1 for root, dirs, files in os.walk(project_path) 
                         for file in files if file.endswith('.java'))
    print(f"📊 Found {java_file_count} Java files to process")
else:
    print(f"❌ Project directory not found: {project_path}")
    print("Please ensure the directory exists in the current workspace.")

# Check if project path exists before processing
if not os.path.exists(project_path):
    print(f"❌ Project path does not exist: {project_path}")
    print("Please ensure the OneInsights directory exists in the current workspace.")
else:
    print(f"🚀 Starting comprehensive Java code analysis...")
    print(f"📁 Processing project: {project_path}")
    
    try:
        # Main extraction
        graph_data = extract_relations_enhanced(project_path)
        
        print(f"\n🎉 Analysis completed successfully!")
        print(f"📊 Results summary:")
        print(f"   - Total nodes: {len(graph_data['nodes'])}")
        print(f"   - Total relations: {len(graph_data['relations'])}")
        print(f"   - Endpoints found: {graph_data['endpoint_summary']['total_endpoints']}")
        print(f"   - Unique paths: {graph_data['endpoint_summary']['unique_paths']}")
        print(f"   - Method mappings: {graph_data['endpoint_summary']['method_mappings']}")
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        import traceback
        traceback.print_exc()
        graph_data = None

# Save to CSV files
if 'graph_data' in locals() and graph_data is not None:
    try:
        print(f"\n💾 Saving results to CSV files...")
        csv_output_path = save_enhanced_graph_to_csv(graph_data, output_dir=CSV_OUTPUT_DIR)
        
        print(f"\n📁 CSV files saved successfully!")
        print(f"📂 Location: {csv_output_path}")
        
        # Show sample data from nodes.csv
        nodes_file = os.path.join(csv_output_path, "nodes.csv")
        if os.path.exists(nodes_file):
            sample_nodes = pd.read_csv(nodes_file).head(3)
            print(f"\n📋 Sample nodes data:")
            print(sample_nodes[['type', 'name', 'full_name']].to_string(index=False))
        
    except Exception as e:
        print(f"❌ Error saving CSV files: {e}")
        import traceback
        traceback.print_exc()
else:
    print("⚠️ No graph data available to save. Please run the analysis first.")

# Push to Neo4j
if 'graph_data' in locals() and graph_data is not None:
    try:
        print(f"\n🔗 Pushing results to Neo4j...")
        push_enhanced_graph_to_neo4j(graph_data, NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD, NEO4J_DB)
        
        print(f"\n🎉 Neo4j upload completed!")
        print(f"🔍 You can now query your data in Neo4j Browser at: http://localhost:7474")
        print(f"\n💡 Sample queries to try:")
        print(f"   • MATCH (n) RETURN labels(n), count(n) - Count nodes by type")
        print(f"   • MATCH (c:Class)-[:HAS_METHOD]->(m:Method) RETURN c.name, count(m) - Methods per class")
        print(f"   • MATCH (f:File)-[:EXPOSES]->(e:Api_endpoint) RETURN f.name, e.path - API endpoints")
        
    except Exception as e:
        print(f"❌ Error pushing to Neo4j: {e}")
        print(f"💡 Make sure Neo4j is running and credentials are correct")
        import traceback
        traceback.print_exc()
else:
    print("⚠️ No graph data available to push. Please run the analysis first.")

# Final summary
if 'graph_data' in locals() and graph_data is not None:
    print("\n🎉 ANALYSIS COMPLETE! 🎉")
    print("=" * 50)
    print(f"📊 FINAL STATISTICS:")
    print(f"   • Total nodes extracted: {len(graph_data['nodes']):,}")
    print(f"   • Total relationships: {len(graph_data['relations']):,}")
    print(f"   • API endpoints found: {graph_data['endpoint_summary']['total_endpoints']:,}")
    print(f"   • Unique endpoint paths: {graph_data['endpoint_summary']['unique_paths']:,}")
    print(f"   • Method mappings: {graph_data['endpoint_summary']['method_mappings']:,}")
    
    print(f"\n📁 OUTPUT LOCATIONS:")
    print(f"   • CSV files: ./{CSV_OUTPUT_DIR}/")
    print(f"   • Neo4j database: {NEO4J_DB}")
    
    print(f"\n🔍 WHAT YOU CAN DO NEXT:")
    print(f"   1. Explore CSV files for detailed data analysis")
    print(f"   2. Query Neo4j for interactive graph exploration")
    print(f"   3. Use lineage data for impact analysis")
    print(f"   4. Analyze code dependencies and relationships")
    
    print(f"\n✨ Happy analyzing! ✨")
else:
    print("\n⚠️ Analysis was not completed successfully.")
    print("Please check the error messages above and try again.")

# Test the fixed regex patterns
test_code_sample = """
@RestController
@RequestMapping("/api/users")
public class UserController {
    
    @GetMapping("/list")
    public List<User> getUsers() {
        return userService.findAll();
    }
    
    @PostMapping("/create")
    public User createUser(@RequestBody User user) {
        return userService.save(user);
    }
    
    @Value("${app.name}")
    private String appName;
    
    @Qualifier("userService")
    private UserService userService;
}
"""

print("🧪 Testing fixed regex patterns...")

# Test API endpoint extraction
test_api_results = extract_api_endpoints(test_code_sample)
print(f"\n📡 API Endpoints found: {len(test_api_results['endpoints'])}")
for endpoint in test_api_results['endpoints']:
    print(f"   - {endpoint['method']}: {endpoint['path']}")

# Test Spring annotation extraction
test_app_extractor = ApplicationNodeExtractor()
test_annotations = test_app_extractor.extract_spring_annotations(test_code_sample)
print(f"\n🏷️ Spring Annotations found: {len(test_annotations)}")
for ann in test_annotations:
    print(f"   - {ann['type']}: {ann['full_match']} (value: {ann['value']})")

print("\n✅ Regex patterns are working correctly!")